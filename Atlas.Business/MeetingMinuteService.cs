using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Data.Entity;
using Atlas.Data.Entities;
using i18next_net;

namespace Atlas.Business
{
    public class MeetingMinuteService : MeetingMinuteGeneratorAbstract
    {
        public string lang { get; set; }
        public bool withActions { get; set; }
        public Content contentMeeting { get; }
        public Workgroup workgroup { get; }
        

        public MeetingMinuteService()
        {

        }

        public MeetingMinuteService(string lang, bool withActions, Content contentMeeting)
        {
            this.lang = lang;
            this.withActions = withActions;
            this.contentMeeting = contentMeeting;
            this.workgroup = contentMeeting.Workgroup;
        }


        public override string buildHeader(DateTime data)
        {

           //string langCode = GetLanguageCode(lang);

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);

            string header = "<span style=\"text-align: center;\" width=\"100%\">" +
                   "<h3>" + (workgroup.Client.fullName != "" ? workgroup.Client.fullName : workgroup.Client.name) + "</h3>" +
                   //"<p>Companhia Aberta de Capital Autorizado</p>" + Não temos esse atributo
                   (workgroup.Client.taxNumber != null && workgroup.Client.taxNumber != "" ? "<p>" + String.Format("<b>" + i18n.t("meetingminute:cnpj") + "</b> {0}", workgroup.Client.taxNumber) + "</p>" : "") +
                   //"<p>NIRE 00000000000</p>" + Não temos esse atributo
                   "</span><br/>";

            string title = "<span style=\"text-align: center\" width=\"100%\">" +
                "<p style=\"text-align: center;\" width=\"100%\"><b>" + String.Format(i18n.t("meetingminute:boardMeetingMinute") + " {0}", workgroup.name.ToUpper()) + "</b></p>" +
                "<br/>" +
                "<p style=\"text-align: center;\" width=\"100%\"><b>" + String.Format(i18n.t("meetingminute:dateTime"), data.Day.ToString(), data.ToString("MMMM", CultureInfo.CreateSpecificCulture(this.lang)).ToUpper(), data.Year.ToString()) + "</b></p>" +
                "</span>";

            return header + title;
        }

        public override string buildDescription(DateTime data, List<ContentSubscriber> subscribers)
        {
            string attendees = "";
            ContentSubscriber userOne = null;

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);
      
            if (subscribers.Any())
            {
                userOne = subscribers.First();

                if (userOne.rsvp == true)
                {
                    attendees += userOne.User.name + " (" + i18n.t("meetingminute:present") + ")" + "; ";
                }
                else if (userOne.rsvp == false)
                {
                    attendees += userOne.User.name + " (" + i18n.t("meetingminute:absent") + ")" + "; ";
                }
                else
                {
                    attendees += userOne.User.name + "; ";
                }
            }

            if (subscribers.Count() > 1)
            {
                foreach (var cs in subscribers.Skip(1))
                {
                    if (cs.rsvp == true)
                    {
                        attendees += cs.User.name + " (" + i18n.t("meetingminute:present") + ")" + "; ";
                    }
                    else if(cs.rsvp == false)
                    {
                        attendees += cs.User.name + " ("+ i18n.t("meetingminute:absent") + ")" + "; ";
                    }
                    else
                    {
                        attendees += cs.User.name + "; " ;
                    }
                }

            }
            string description = "";

            if (lang == "en")
            {
                 description = "<span><p style= \"padding-right: 50px; padding-right: 70px;\">" + "<ol><li>" + String.Format(
                           "<b>" + i18n.t("meetingminute:placeTime") +  "</b>" + i18n.t("meetingminute:placeTimeBody"),
                            data.Month.ToString(), data.Day.ToString(), data.Year.ToString(), data.Hour.ToString(), data.Minute.ToString(), contentMeeting.Meeting.FirstOrDefault().location           
                           ) + "</li>" +
                           "<li>" + String.Format("<b>" + i18n.t("meetingminute:attendees") + "</b> {0}", attendees) + "</li>";
            }
            else
            {
                 description = "<span><p style= \"padding-right: 50px; padding-right: 70px;\">" + "<ol><li>" + String.Format(
                           "<b>" + i18n.t("meetingminute:placeTime") + "</b>" + i18n.t("meetingminute:placeTimeBody"),
                             data.Day.ToString(), data.Month.ToString(), data.Year.ToString(), data.Hour.ToString(), data.Minute.ToString(), contentMeeting.Meeting.FirstOrDefault().location
                           ) + "</li>" +
                           "<li>" + String.Format("<b>" + i18n.t("meetingminute:attendees") + "</b> {0}", attendees) + "</li>";
            }
            
            return description;

        }

        public string buildGuests(IEnumerable<string> guests)
        {
            if (guests.Count() == 0) return "";

            string attendees = "";

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);

            attendees += "<li>" + String.Format("<b>" + i18n.t("meetingminute:guests") + "</b> ");

            if (guests.Any())
            {
                attendees += guests.First();
            }

            if (guests.Count() > 1)
            {
                foreach (var guest in guests.Skip(1))
                {
                    attendees += "; " + guest;
                }

            }

            attendees += "</li>";

            return attendees;
        }

        public override string buildAgendaItems()
        {
            string agenda = "";

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);
            
            var item = contentMeeting.Child_Content.Where(m => m.type == "MeetingAgendaItem").SelectMany(o => o.MeetingAgendaItem).OrderBy(o => o.itemOrder).Select(o => o.Content);
            foreach (Content it in item)
            {
                if(it.title == "MEETING_BREAK")
                { 
                    agenda += String.Format("<li> {0} </li>", i18n.t("meetingminute:interval"));
                }
                else
                {
                    agenda += String.Format("<li> {0} </li>", it.title.Replace("\n", "<br/>"));
                }
               
            }

            string agendaItemsList = "<li><b>"+ i18n.t("meetingminute:agenda") + "</b><br/><ul>" + agenda + "</ul></li>";

            return agendaItemsList;
        }

        public override string buildPoll(int _currentUserId)
        {

            var pollsFromMeeting = contentMeeting.Child_Content.Where(p => p.type == "Poll" && p.deleted != true && p.status != "CANCELLED");

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);

            if (pollsFromMeeting.Count() > 0)
            {
                // There are polls voted
                string pollsItems = "<ul>";

                foreach (Content p in pollsFromMeeting)
                {
                    pollsItems += String.Format("<li>" + p.title);
                    pollsItems += String.Format("<p> {0}" + i18n.t("meetingminute:voters"), p.ContentSubscriber.Count(), p.Poll.FirstOrDefault().Votes.Where(v => !v.deleted.GetValueOrDefault()).Count());

                    // Calculating result
                    if (p.status == "CLOSED")
                    {
                        //If the poll has two votes or more enter inside the if
                        if (p.Poll.FirstOrDefault().Options.ToList().Count() > 1)
                        {
                            List<PollOption> sortedOptions = p.Poll.FirstOrDefault().Options.ToList();

                            sortedOptions.Sort(delegate (PollOption a, PollOption b)
                            {
                                if (a.Votes.Count(v => !v.deleted.GetValueOrDefault()) == b.Votes.Count(v => !v.deleted.GetValueOrDefault()))
                                {
                                    return 0;
                                }
                                else if (a.Votes.Count(v => !v.deleted.GetValueOrDefault()) > b.Votes.Count(v => !v.deleted.GetValueOrDefault()))
                                {
                                    return 1;
                                }
                                else
                                {
                                    return -1;
                                }
                            });

                            if (sortedOptions.LastOrDefault().Votes.Count(v => !v.deleted.GetValueOrDefault()) > 0)
                            {
                                int topVoted_count = 0;
                                bool isTied = false;
                                int tiedOption = 0;

                                var poll = p.Poll.FirstOrDefault();
                                PollOption topVotedOption = null;

                                foreach (var option in sortedOptions)
                                {
                                    if (poll.pollType == "APPROVAL" && option.title == "ABSTAIN")
                                    {
                                        continue;
                                    }
                                    if (option.Votes.Count(v => !v.deleted.GetValueOrDefault()) > topVoted_count)
                                    {
                                        topVotedOption = option;
                                        topVoted_count = option.Votes.Count(v => !v.deleted.GetValueOrDefault());
                                    }
                                }

                                foreach (var option in sortedOptions)
                                {
                                    if (poll.pollType == "APPROVAL" && option.title == "ABSTAIN")
                                    {
                                        continue;
                                    }
                                    if (option.Votes.Count(v => !v.deleted.GetValueOrDefault()) == topVoted_count && topVoted_count > 0)
                                    {
                                        tiedOption++;
                                    }
                                }

                                string _title = topVotedOption?.title ?? sortedOptions.LastOrDefault().title;
                                if (tiedOption == 1)
                                {
                                    isTied = false;
                                }
                                else
                                {
                                    isTied = true;
                                }

                                if (isTied)
                                {
                                    _title = i18n.t("meetingminute:resultTied");
                                }
                                else
                                {

                                    switch (_title.ToLower())
                                    {
                                        case "yes":
                                            _title = i18n.t("meetingminute:yes_option");
                                            break;
                                        case "no":
                                            _title = i18n.t("meetingminute:not_option");
                                            break;
                                        case "abstain":
                                            _title = i18n.t("meetingminute:abstain_option");
                                            break;
                                        default:
                                            break;
                                    }
                                }

                                pollsItems += String.Format(i18n.t("meetingminute:result") + " " + _title);
                            }
                            else
                            {
                                pollsItems += "</p> </li> ";
                            }

                        }
                        //If the poll has just one vote enter inside the else 
                        else
                        {
                            pollsItems += String.Format(i18n.t("meetingminute:result") + p.Poll.FirstOrDefault().Options.ToList().FirstOrDefault().title);
                        }
                    }
                }

                string polls = "<b>" + i18n.t("meetingminute:deliberation") + "</b><br/>" + pollsItems + "</ul>";
                return polls;
            }
            else
            {
                return "";
            }

        }

        public override string buildTasks(int _currentUserId)
        {

            var actionsList = contentMeeting.Child_Content.Where(p => p.type == "Task");

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18n.changeLanguage(lang);

            if (withActions)
            {
                // Setting up Tasks
                string actions = "<b>" + i18n.t("meetingminute:tasks") + "</b>";

                actions += "<ul id=\"action-list\">";
                if (actionsList.Count() > 0)
                {
                    foreach (Content t in actionsList)
                    {
                        string action_id = $"id=\"action-{t.contentId}\"";
                        actions += $"<li {action_id}>" + String.Format(i18n.t("meetingminute:delegatedTask"), t.User_Create.name, t.title, t.User_Assigned.name) + "</li>";
                    }
                }
                actions += "</ul>";

                return actions;
            }

            return "";
        }
    }
}
